import { useEffect, useRef, useState } from 'react';
import './CaptureScreen.css';

interface CaptureScreenData {
  thumbnail: string;
  id: string;
  name: string;
  display_id: string;
  width: number;
  height: number;
}


const CaptureScreen = () => {
  const [screenData, setScreenData] = useState<CaptureScreenData | null>(null);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const originalImageRef = useRef<HTMLImageElement | null>(null);

  // 监听截图数据
  useEffect(() => {
    console.log('设置截图数据监听器');
    const removeDataListener = window.electronAPI.onCaptureScreenData((data) => {
      console.log('收到截图数据', data);
      setScreenData(data);
    });
    
    // 监听直接截图模式
    const removeDirectCaptureListener = window.electronAPI.onStartDirectCapture((data) => {
      console.log('启动直接截图模式', data);
      // 直接截图模式不需要加载图像，直接设置为已加载状态
      setIsImageLoaded(true);
    });

    return () => {
      removeDataListener();
      removeDirectCaptureListener();
    };
  }, []);

  return (
    <div className="capture-screen">
      {screenData && (
        <img
          ref={imageRef}
          src={screenData?.thumbnail || ''}
          alt="Screen"
          style={{ display: 'none' }}
        />
      )}
      <canvas
        ref={canvasRef}
        className="capture-canvas"
      />
      <div className="capture-info">
        123
      </div>
      <div className="capture-instructions">
        拖动鼠标选择要截取的区域
      </div>
    </div>
  );
};

export default CaptureScreen;