import { useState, useEffect } from "react";
import "./App.css";

function App() {
  // 开始截图
  const handleStartCapture = async () => {
    try {
      await window.electronAPI.startScreenCapture();
    } catch (error) {
      console.error("截图失败:", error);
    }
  };

  return (
    <div className="app-container">
      <h1>快速屏幕截图工具</h1>

      <div className="actions">
        <button className="capture-btn" onClick={handleStartCapture}>
          开始截图
        </button>
      </div>
    </div>
  );
}

export default App;
